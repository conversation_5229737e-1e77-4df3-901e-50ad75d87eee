import tushare as ts
from snownlp import SnowNLP
import pandas as pd
from datetime import datetime, timedelta
import time
import logging
from typing import Dict, List, Optional, Tuple
from dataNew import ts_pro

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class NewsSentimentAnalyzer:
    def __init__(self):
        """
        初始化新闻情绪分析器
        
        Args:
            tushare_token: Tushare API token
        """
        self.pro = ts_pro()
        self.cache = {}  # 缓存新闻数据，避免重复请求
        
    def get_news(self, 
                src: str = 'sina', 
                start_date: Optional[str] = None, 
                end_date: Optional[str] = None,
                max_retries: int = 3) -> pd.DataFrame:
        """
        获取新闻数据
        
        Args:
            src: 新闻来源，如'sina'、'10jqka'等
            start_date: 开始时间，格式'YYYY-MM-DD HH:MM:SS'
            end_date: 结束时间，格式'YYYY-MM-DD HH:MM:SS'
            max_retries: 最大重试次数
            
        Returns:
            pd.DataFrame: 包含新闻数据的DataFrame
        """
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
        cache_key = f"{src}_{start_date}_{end_date}"
        if cache_key in self.cache:
            return self.cache[cache_key]
            
        for attempt in range(max_retries):
            try:
                df = self.pro.news(src=src, start_date=start_date, end_date=end_date)
                self.cache[cache_key] = df
                return df
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"获取新闻数据失败: {e}")
                    return pd.DataFrame()
                time.sleep(1)  # 等待1秒后重试
                
    def analyze_sentiment(self, text: str) -> Tuple[float, str]:
        """
        分析文本情绪
        
        Args:
            text: 待分析文本
            
        Returns:
            Tuple[float, str]: (情绪分数, 情绪标签)
        """
        if not text or not isinstance(text, str):
            return 0.5, "neutral"
            
        try:
            s = SnowNLP(text)
            score = s.sentiments  # 情绪分数，0-1之间，>0.5为正面，<0.5为负面
            
            # 根据分数分类
            if score > 0.6:
                sentiment = "positive"
            elif score < 0.4:
                sentiment = "negative"
            else:
                sentiment = "neutral"
                
            return score, sentiment
        except Exception as e:
            logger.error(f"情绪分析出错: {e}")
            return 0.5, "neutral"
            
    def process_news_with_sentiment(self, 
                                  src: str = 'sina', 
                                  start_date: Optional[str] = None, 
                                  end_date: Optional[str] = None) -> pd.DataFrame:
        """
        获取新闻并分析情绪
        
        Args:
            src: 新闻来源
            start_date: 开始时间
            end_date: 结束时间
            
        Returns:
            pd.DataFrame: 包含新闻和情绪分析结果的DataFrame
        """
        # 获取新闻数据
        df = self.get_news(src=src, start_date=start_date, end_date=end_date)
        
        if df.empty:
            logger.warning("未获取到新闻数据")
            return pd.DataFrame()
            
        # 分析每条新闻的情绪
        sentiment_results = [self.analyze_sentiment(content) for content in df.get('content')]
        
        # 添加情绪分析结果到DataFrame
        sentiment_scores, sentiment_labels = zip(*sentiment_results)
        df['sentiment_score'] = sentiment_scores
        df['sentiment_label'] = sentiment_labels
        
        # 转换时间列为datetime类型
        if 'datetime' in df.columns:
            df['datetime'] = pd.to_datetime(df['datetime'])
            
        return df
    
    def get_sentiment_summary(self, 
                            src: str = 'sina', 
                            start_date: Optional[str] = None, 
                            end_date: Optional[str] = None) -> Dict:
        """
        获取新闻情绪摘要
        
        Args:
            src: 新闻来源
            start_date: 开始时间
            end_date: 结束时间
            
        Returns:
            Dict: 包含情绪摘要的字典
        """
        df = self.process_news_with_sentiment(src, start_date, end_date)
        
        if df.empty:
            return {
                'total_news': 0,
                'positive_ratio': 0,
                'negative_ratio': 0,
                'neutral_ratio': 0,
                'avg_sentiment': 0.5,
                'sentiment_trend': 'neutral'
            }
            
        total = len(df)
        positive = len(df[df['sentiment_label'] == 'positive'])
        negative = len(df[df['sentiment_label'] == 'negative'])
        neutral = len(df[df['sentiment_label'] == 'neutral'])
        
        # 计算情绪趋势（与上一时间段比较）
        prev_end = (pd.to_datetime(start_date) - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S')
        prev_start = (pd.to_datetime(start_date) - timedelta(hours=2)).strftime('%Y-%m-%d %H:%M:%S')
        prev_df = self.process_news_with_sentiment(src, prev_start, prev_end)
        
        prev_avg = prev_df['sentiment_score'].mean() if not prev_df.empty else 0.5
        current_avg = df['sentiment_score'].mean()
        
        if current_avg > prev_avg + 0.1:
            trend = 'improving'
        elif current_avg < prev_avg - 0.1:
            trend = 'deteriorating'
        else:
            trend = 'stable'
            
        return {
            'total_news': total,
            'positive_ratio': positive / total if total > 0 else 0,
            'negative_ratio': negative / total if total > 0 else 0,
            'neutral_ratio': neutral / total if total > 0 else 0,
            'avg_sentiment': current_avg,
            'sentiment_trend': trend,
            'news_samples': df[['content', 'sentiment_score', 'sentiment_label']].head(5).to_dict('records')
        }


# 使用示例
if __name__ == "__main__":
    
    # 初始化分析器
    analyzer = NewsSentimentAnalyzer()
    
    # 获取并分析新闻
    end_time = datetime.now()
    start_time = end_time - timedelta(days=1)
    
    # 获取新闻并分析情绪
    # news_df = analyzer.process_news_with_sentiment(
    #     src='sina',
    #     start_date=start_time.strftime('%Y-%m-%d %H:%M:%S'),
    #     end_date=end_time.strftime('%Y-%m-%d %H:%M:%S')
    # )
    #
    # if not news_df.empty:
    #     print("\n新闻情绪分析结果:")
    #     print(news_df[['title', 'sentiment_score', 'sentiment_label']].head())
        
    # 获取情绪摘要
    summary = analyzer.get_sentiment_summary(
        src='sina',
        start_date=start_time.strftime('%Y-%m-%d %H:%M:%S'),
        end_date=end_time.strftime('%Y-%m-%d %H:%M:%S')
    )

    print("\n情绪摘要:")
    print(f"总新闻数: {summary['total_news']}")
    print(f"正面新闻比例: {summary['positive_ratio']*100:.2f}%")
    print(f"负面新闻比例: {summary['negative_ratio']*100:.2f}%")
    print(f"中性新闻比例: {summary['neutral_ratio']*100:.2f}%")
    print(f"平均情绪分数: {summary['avg_sentiment']:.4f}")
    print(f"情绪趋势: {summary['sentiment_trend']}")

    print("\n新闻样例:")
    for i, news in enumerate(summary['news_samples'], 1):
        print(f"\n{i}. {news['content']}")
        print(f"   情绪分数: {news['sentiment_score']:.4f} ({news['sentiment_label']})")